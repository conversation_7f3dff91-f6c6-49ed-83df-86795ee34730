[{"/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/AboutSection.tsx": "1", "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/ContactSection.tsx": "2", "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/ExperienceSection.tsx": "3", "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/HeroSection.tsx": "4", "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/HobbiesSection.tsx": "5", "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/Navigation.tsx": "6", "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/TechStackSection.tsx": "7", "/Users/<USER>/Documents/working/frontend/resume-job/src/app/layout.tsx": "8", "/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx": "9", "/Users/<USER>/Documents/working/frontend/resume-job/src/app/not-found.tsx": "10"}, {"size": 1895, "mtime": 1753943167347, "results": "11", "hashOfConfig": "12"}, {"size": 3133, "mtime": 1753937402235, "results": "13", "hashOfConfig": "12"}, {"size": 2399, "mtime": 1753937297700, "results": "14", "hashOfConfig": "12"}, {"size": 1489, "mtime": 1753943000204, "results": "15", "hashOfConfig": "12"}, {"size": 2148, "mtime": 1753937322886, "results": "16", "hashOfConfig": "12"}, {"size": 1851, "mtime": 1753942935175, "results": "17", "hashOfConfig": "12"}, {"size": 2033, "mtime": 1753937311211, "results": "18", "hashOfConfig": "12"}, {"size": 1195, "mtime": 1753947535400, "results": "19", "hashOfConfig": "12"}, {"size": 762, "mtime": 1753942707953, "results": "20", "hashOfConfig": "12"}, {"size": 829, "mtime": 1753947591952, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "asc02d", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/AboutSection.tsx", [], [], "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/ContactSection.tsx", [], [], "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/ExperienceSection.tsx", [], [], "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/HeroSection.tsx", [], [], "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/HobbiesSection.tsx", [], [], "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/Navigation.tsx", [], [], "/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/TechStackSection.tsx", [], [], "/Users/<USER>/Documents/working/frontend/resume-job/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/working/frontend/resume-job/src/app/page.tsx", [], [], "/Users/<USER>/Documents/working/frontend/resume-job/src/app/not-found.tsx", [], []]