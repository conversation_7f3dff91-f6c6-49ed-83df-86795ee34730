/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[7].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[7].use[5]!./src/app/assets/styles/HeroSection.module.scss ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.HeroSection_heroSection__Stlcl {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.HeroSection_heroContent__q_RZw {
  text-align: center;
  z-index: 10;
}

.HeroSection_avatarContainer__iPik9 {
  margin-bottom: 2rem;
}
.HeroSection_avatarContainer__iPik9 .HeroSection_avatar__iPS9X {
  width: 8rem;
  height: 8rem;
  margin: 0 auto 1.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.25rem;
  font-weight: bold;
  color: white;
  background-image: url(/_next/static/media/avatar.8d9d9285.png);
  background-size: cover;
}

.HeroSection_heroTitle__mjCCO {
  font-size: 3rem;
  font-weight: bold;
  color: white;
  margin-bottom: 1.5rem;
}
@media (min-width: 768px) {
  .HeroSection_heroTitle__mjCCO {
    font-size: 4.5rem;
  }
}
.HeroSection_heroTitle__mjCCO .HeroSection_nameHighlight__nwKKw {
  background: linear-gradient(to right, #a855f7, #ec4899);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.HeroSection_heroSubtitle__ymOod {
  font-size: 1.25rem;
  color: #d1d5db;
  margin-bottom: 2rem;
  max-width: 42rem;
  margin-left: auto;
  margin-right: auto;
}
@media (min-width: 768px) {
  .HeroSection_heroSubtitle__ymOod {
    font-size: 1.5rem;
  }
}

.HeroSection_ctaButton__Ww2Up {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 2rem;
  background: linear-gradient(to right, #9333ea, #db2777);
  color: white;
  font-weight: 500;
  border-radius: 9999px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.HeroSection_ctaButton__Ww2Up:hover {
  transform: scale(1.05);
}
.HeroSection_ctaButton__Ww2Up:hover {
  background: linear-gradient(to right, #7c3aed, #be185d);
}
.HeroSection_ctaButton__Ww2Up .HeroSection_ctaIcon__voAYT {
  margin-left: 0.5rem;
  height: 1.25rem;
  width: 1.25rem;
}

.HeroSection_backgroundEffects__9BS4T {
  position: absolute;
  inset: 0;
  overflow: hidden;
}
.HeroSection_backgroundEffects__9BS4T .HeroSection_floatingOrb__SchjG {
  position: absolute;
  width: 20rem;
  height: 20rem;
  border-radius: 50%;
  mix-blend-mode: multiply;
  filter: blur(40px);
  opacity: 0.2;
  animation: HeroSection_pulse__lmUeK 4s ease-in-out infinite;
}
.HeroSection_backgroundEffects__9BS4T .HeroSection_floatingOrb__SchjG.HeroSection_orb1__PxQPk {
  top: -10rem;
  right: -10rem;
  background-color: #a855f7;
}
.HeroSection_backgroundEffects__9BS4T .HeroSection_floatingOrb__SchjG.HeroSection_orb2__jfaid {
  bottom: -10rem;
  left: -10rem;
  background-color: #ec4899;
  animation-delay: 2s;
}

@keyframes HeroSection_pulse__lmUeK {
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.4;
  }
}
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[7].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[7].use[5]!./src/app/assets/styles/Navigation.module.scss ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.Navigation_navigation__mOJV5 {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 50;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
}
.Navigation_navigation__mOJV5 .Navigation_container__k_2_2 {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}
@media (min-width: 640px) {
  .Navigation_navigation__mOJV5 .Navigation_container__k_2_2 {
    padding: 0 1.5rem;
  }
}
@media (min-width: 1024px) {
  .Navigation_navigation__mOJV5 .Navigation_container__k_2_2 {
    padding: 0 2rem;
  }
}
.Navigation_navigation__mOJV5 .Navigation_navContent__7MjXE {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
}
.Navigation_navigation__mOJV5 .Navigation_logo__nAQb1 {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  cursor: pointer;
}
.Navigation_navigation__mOJV5 .Navigation_navLinks__I3nIo {
  display: none;
  gap: 2rem;
}
@media (min-width: 768px) {
  .Navigation_navigation__mOJV5 .Navigation_navLinks__I3nIo {
    display: flex;
  }
}
.Navigation_navigation__mOJV5 .Navigation_navButton__yk87O {
  font-size: 0.875rem;
  font-weight: 500;
  color: #efdcf1;
  background: none;
  border: none;
  cursor: pointer;
  transition: color 0.3s ease;
  padding: 12px;
  border-radius: 8px;
  font-weight: 600;
}
.Navigation_navigation__mOJV5 .Navigation_navButton__yk87O:hover {
  color: #a855f7;
  background-color: #e1e2e3;
}
.Navigation_navigation__mOJV5 .Navigation_navButton__yk87O.Navigation_active__WhBBR {
  color: #a855f7;
}
