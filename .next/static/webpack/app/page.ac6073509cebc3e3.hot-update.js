"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/components/Navigation.tsx":
/*!*******************************************!*\
  !*** ./src/app/components/Navigation.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_assets_styles_Navigation_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/assets/styles/Navigation.module.scss */ \"(app-pages-browser)/./src/app/assets/styles/Navigation.module.scss\");\n/* harmony import */ var _app_assets_styles_Navigation_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_app_assets_styles_Navigation_module_scss__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction Navigation() {\n    _s();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('hero');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navigation.useEffect\": ()=>{\n            const handleScroll = {\n                \"Navigation.useEffect.handleScroll\": ()=>{\n                    const sections = [\n                        'hero',\n                        'about',\n                        'experience',\n                        'techstack',\n                        'hobbies',\n                        'contact'\n                    ];\n                    const scrollPosition = window.scrollY + 200;\n                    for (const section of sections){\n                        const element = document.getElementById(section);\n                        if (element) {\n                            const { offsetTop, offsetHeight } = element;\n                            if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {\n                                setActiveSection(section);\n                                break;\n                            }\n                        }\n                    }\n                }\n            }[\"Navigation.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"Navigation.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Navigation.useEffect\"];\n        }\n    }[\"Navigation.useEffect\"], []);\n    const scrollToSection = (sectionId)=>{\n        const element = document.getElementById(sectionId);\n        if (element) {\n            element.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    const backToTop = ()=>{\n        window.scrollTo({\n            top: 0,\n            behavior: 'smooth'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: (_app_assets_styles_Navigation_module_scss__WEBPACK_IMPORTED_MODULE_2___default().navigation),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_app_assets_styles_Navigation_module_scss__WEBPACK_IMPORTED_MODULE_2___default().container),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_app_assets_styles_Navigation_module_scss__WEBPACK_IMPORTED_MODULE_2___default().navContent),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_app_assets_styles_Navigation_module_scss__WEBPACK_IMPORTED_MODULE_2___default().logo),\n                        children: \"Nguyen Thanh Nam\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/Navigation.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_app_assets_styles_Navigation_module_scss__WEBPACK_IMPORTED_MODULE_2___default().navLinks),\n                        children: [\n                            'About',\n                            'Experience',\n                            'Tech Stack',\n                            'Hobbies',\n                            'Contact'\n                        ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>scrollToSection(item.toLowerCase().replace(' ', '')),\n                                className: \"\".concat((_app_assets_styles_Navigation_module_scss__WEBPACK_IMPORTED_MODULE_2___default().navButton), \" \").concat(activeSection === item.toLowerCase().replace(' ', '') ? (_app_assets_styles_Navigation_module_scss__WEBPACK_IMPORTED_MODULE_2___default().active) : ''),\n                                children: item\n                            }, item, false, {\n                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/Navigation.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/Navigation.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/Navigation.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/Navigation.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/Navigation.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n_s(Navigation, \"4ZXzqJncAw/6y42xu/qTHhBEkpc=\");\n_c = Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/Navigation.tsx\n"));

/***/ })

});