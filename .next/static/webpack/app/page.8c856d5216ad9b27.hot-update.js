/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/assets/styles/Navigation.module.scss":
/*!******************************************************!*\
  !*** ./src/app/assets/styles/Navigation.module.scss ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"navigation\":\"Navigation_navigation__mOJV5\",\"container\":\"Navigation_container__k_2_2\",\"navContent\":\"Navigation_navContent__7MjXE\",\"logo\":\"Navigation_logo__nAQb1\",\"navLinks\":\"Navigation_navLinks__I3nIo\",\"navButton\":\"Navigation_navButton__yk87O\",\"active\":\"Navigation_active__WhBBR\"};\n    if(true) {\n      // 1753949288860\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  \nmodule.exports.__checksum = \"a1944ed93252\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYXNzZXRzL3N0eWxlcy9OYXZpZ2F0aW9uLm1vZHVsZS5zY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0Esa0JBQWtCO0FBQ2xCLE9BQU8sSUFBVTtBQUNqQjtBQUNBLHNCQUFzQixtQkFBTyxDQUFDLHdNQUFzSSxjQUFjLHNEQUFzRDtBQUN4TyxNQUFNLFVBQVU7QUFDaEI7QUFDQTtBQUNBO0FBQ0EseUJBQXlCIiwic291cmNlcyI6WyIvVXNlcnMvbmFtL0RvY3VtZW50cy93b3JraW5nL2Zyb250ZW5kL3Jlc3VtZS1qb2Ivc3JjL2FwcC9hc3NldHMvc3R5bGVzL05hdmlnYXRpb24ubW9kdWxlLnNjc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gZXh0cmFjdGVkIGJ5IG1pbmktY3NzLWV4dHJhY3QtcGx1Z2luXG5tb2R1bGUuZXhwb3J0cyA9IHtcIm5hdmlnYXRpb25cIjpcIk5hdmlnYXRpb25fbmF2aWdhdGlvbl9fbU9KVjVcIixcImNvbnRhaW5lclwiOlwiTmF2aWdhdGlvbl9jb250YWluZXJfX2tfMl8yXCIsXCJuYXZDb250ZW50XCI6XCJOYXZpZ2F0aW9uX25hdkNvbnRlbnRfXzdNalhFXCIsXCJsb2dvXCI6XCJOYXZpZ2F0aW9uX2xvZ29fX25BUWIxXCIsXCJuYXZMaW5rc1wiOlwiTmF2aWdhdGlvbl9uYXZMaW5rc19fSTNuSW9cIixcIm5hdkJ1dHRvblwiOlwiTmF2aWdhdGlvbl9uYXZCdXR0b25fX3lrODdPXCIsXCJhY3RpdmVcIjpcIk5hdmlnYXRpb25fYWN0aXZlX19XaEJCUlwifTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICAvLyAxNzUzOTQ5Mjg4ODYwXG4gICAgICB2YXIgY3NzUmVsb2FkID0gcmVxdWlyZShcIi9Vc2Vycy9uYW0vRG9jdW1lbnRzL3dvcmtpbmcvZnJvbnRlbmQvcmVzdW1lLWpvYi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL21pbmktY3NzLWV4dHJhY3QtcGx1Z2luL2htci9ob3RNb2R1bGVSZXBsYWNlbWVudC5qc1wiKShtb2R1bGUuaWQsIHtcInB1YmxpY1BhdGhcIjpcIi9fbmV4dC9cIixcImVzTW9kdWxlXCI6ZmFsc2UsXCJsb2NhbHNcIjp0cnVlfSk7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoY3NzUmVsb2FkKTtcbiAgICAgIFxuICAgIH1cbiAgXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJhMTk0NGVkOTMyNTJcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/assets/styles/Navigation.module.scss\n"));

/***/ })

});