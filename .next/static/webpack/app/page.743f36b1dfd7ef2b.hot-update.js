"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/components/HeroSection.tsx":
/*!********************************************!*\
  !*** ./src/app/components/HeroSection.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_assets_styles_HeroSection_module_scss__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/assets/styles/HeroSection.module.scss */ \"(app-pages-browser)/./src/app/assets/styles/HeroSection.module.scss\");\n/* harmony import */ var _app_assets_styles_HeroSection_module_scss__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_app_assets_styles_HeroSection_module_scss__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction HeroSection() {\n    const scrollToSection = (sectionId)=>{\n        const element = document.getElementById(sectionId);\n        if (element) {\n            element.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"hero\",\n        className: (_app_assets_styles_HeroSection_module_scss__WEBPACK_IMPORTED_MODULE_1___default().heroSection),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_app_assets_styles_HeroSection_module_scss__WEBPACK_IMPORTED_MODULE_1___default().heroContent),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_app_assets_styles_HeroSection_module_scss__WEBPACK_IMPORTED_MODULE_1___default().avatarContainer),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_app_assets_styles_HeroSection_module_scss__WEBPACK_IMPORTED_MODULE_1___default().avatar)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/HeroSection.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/HeroSection.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: (_app_assets_styles_HeroSection_module_scss__WEBPACK_IMPORTED_MODULE_1___default().heroTitle),\n                        children: [\n                            \"Hi, I'm \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_app_assets_styles_HeroSection_module_scss__WEBPACK_IMPORTED_MODULE_1___default().nameHighlight),\n                                children: \"Nam\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/HeroSection.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 24\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/HeroSection.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: (_app_assets_styles_HeroSection_module_scss__WEBPACK_IMPORTED_MODULE_1___default().heroSubtitle),\n                        children: \"Frontend Developer passionate about creating beautiful, functional web experiences\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/HeroSection.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>scrollToSection('about'),\n                        className: (_app_assets_styles_HeroSection_module_scss__WEBPACK_IMPORTED_MODULE_1___default().ctaButton),\n                        children: [\n                            \"Get to know me\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: (_app_assets_styles_HeroSection_module_scss__WEBPACK_IMPORTED_MODULE_1___default().ctaIcon),\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/HeroSection.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/HeroSection.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/HeroSection.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/HeroSection.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_app_assets_styles_HeroSection_module_scss__WEBPACK_IMPORTED_MODULE_1___default().backgroundEffects),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat((_app_assets_styles_HeroSection_module_scss__WEBPACK_IMPORTED_MODULE_1___default().floatingOrb), \" \").concat((_app_assets_styles_HeroSection_module_scss__WEBPACK_IMPORTED_MODULE_1___default().orb1))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/HeroSection.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat((_app_assets_styles_HeroSection_module_scss__WEBPACK_IMPORTED_MODULE_1___default().floatingOrb), \" \").concat((_app_assets_styles_HeroSection_module_scss__WEBPACK_IMPORTED_MODULE_1___default().orb2))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/HeroSection.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/HeroSection.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/working/frontend/resume-job/src/app/components/HeroSection.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n_c = HeroSection;\nvar _c;\n$RefreshReg$(_c, \"HeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/HeroSection.tsx\n"));

/***/ })

});