// AboutSection regular SCSS (not CSS Modules)
@import './variables';

.about-section {
  padding: 5rem 1rem;
  
  @media (min-width: 640px) {
    padding: 5rem 1.5rem;
  }
  
  @media (min-width: 1024px) {
    padding: 5rem 2rem;
  }
  
  .about-container {
    max-width: 64rem;
    margin: 0 auto;
  }
  
  .about-title {
    font-size: 2.25rem;
    font-weight: bold;
    color: white;
    margin-bottom: 3rem;
    text-align: center;
  }
  
  .about-grid {
    display: grid;
    gap: 3rem;
    align-items: center;
    
    @media (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  .about-content {
    .about-text {
      font-size: 1.125rem;
      color: #d1d5db;
      margin-bottom: 1.5rem;
      line-height: 1.75;
    }
    
    .about-badges {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      
      .badge {
        display: flex;
        align-items: center;
        color: $primary-purple;
        
        .badge-icon {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.5rem;
        }
      }
    }
  }
  
  .about-visual {
    position: relative;
    
    .visual-card {
      width: 100%;
      height: 20rem;
      background: linear-gradient(to bottom right, #9333ea, #db2777);
      border-radius: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 3.75rem;
      font-weight: bold;
      color: white;
    }
  }
}
