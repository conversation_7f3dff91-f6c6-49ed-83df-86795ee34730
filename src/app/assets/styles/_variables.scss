// SCSS Variables
$background-light: #ffffff;
$foreground-light: #171717;
$background-dark: #0a0a0a;
$foreground-dark: #ededed;

// Portfolio specific variables
$primary-purple: #a855f7;
$primary-pink: #ec4899;
$glass-bg: rgba(255, 255, 255, 0.05);
$glass-border: rgba(255, 255, 255, 0.1);

// Custom SCSS mixins for common patterns
@mixin glass-card {
  background: $glass-bg;
  backdrop-filter: blur(12px);
  border: 1px solid $glass-border;
  border-radius: 0.75rem;
}

@mixin gradient-text {
  background: linear-gradient(to right, $primary-purple, $primary-pink);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

@mixin hover-scale {
  transition: all 0.3s ease;
  &:hover {
    transform: scale(1.05);
  }
}
