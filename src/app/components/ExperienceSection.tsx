export default function ExperienceSection() {
  const experiences = [
    {
      title: "FrontEnd Developer",
      company: "CharterTech Company",
      period: "06/2025 - Present",
      description: "Led frontend development for multiple web applications using React, Next.js, and TypeScript. Collaborated with design and backend teams to deliver high-quality user experiences.",
      technologies: ["React", "Next.js", "TypeScript", "Tailwind CSS"]
    },
    {
      title: "Frontend Developer",
      company: "Kiotviet",
      period: "2023 - 2025",
      description: "Developed responsive websites and web applications for various clients. Focused on performance optimization and accessibility standards.",
      technologies: ["React", "Next.js", "TypeScript", "Tailwind CSS"]
    },
    {
      title: "Frontend Developer",
      company: "Open Commerce Global",
      period: "2020 - 2023",
      description: "Started my journey in web development, working on various projects and learning modern development practices.",
      technologies: ["Vuejs", "Vuex", "Nuxt"]
    }
  ];

  return (
    <section id="experience" className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20">
      <div className="max-w-6xl mx-auto">
        <h2 className="text-4xl font-bold text-white mb-12 text-center">Experience</h2>
        <div className="space-y-8">
          {experiences.map((job, index) => (
            <div key={index} className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                <div>
                  <h3 className="text-xl font-semibold text-white">{job.title}</h3>
                  <p className="text-purple-400 font-medium">{job.company}</p>
                </div>
                <span className="text-gray-400 text-sm mt-2 md:mt-0">{job.period}</span>
              </div>
              <p className="text-gray-300 mb-4">{job.description}</p>
              <div className="flex flex-wrap gap-2">
                {job.technologies.map((tech, techIndex) => (
                  <span key={techIndex} className="px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm">
                    {tech}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
