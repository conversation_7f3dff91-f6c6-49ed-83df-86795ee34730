type TechStack = {
  category: string;
  icon: string;
  technologies: {
    title: string,
    level: number
  }[]
}

export default function TechStackSection() {
  const techStacks: TechStack[] = [
    {
      category: "Frontend",
      icon: "🎨",
      technologies: [
        {
        title: "React",
        level: 4
      },
      {
        title: "VueJ<PERSON>",
        level: 4
      },
      {
        title: "Redux",
        level: 4
      },
      {
        title: "Zustand",
        level: 3
      },
      {
        title: "NextJS",
        level: 4
      },
      {
        title: "TailwindCSS",
        level: 4
      },
      {
        title: "Sass",
        level: 4
      },    
    ]
    },
    {
      category: "Tools & Others",
      icon: "🛠️",
      technologies: [
        {
          title: "Git",
          level: 4
        },
        {
          title: "Figma",
          level: 4
        },
        {
          title: "Jest",
          level: 3
        }
      ]
    },
    {
      category: "Backend & Database",
      icon: "⚙️",
      technologies: [
        {
          title: "NodeJS",
          level: 4
        },
        {
          title: "Express",
          level: 4
        },
        {
          title: "MongoDB",
          level: 4
        },
        {
          title: "MySQL",
          level: 3
        },
        {
          title: "Golang",
          level: 3
        },

      ],
    }
  ];

  return (
    <section id="techstack" className="py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        <h2 className="text-4xl font-bold text-white mb-12 text-center">Tech Stack</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {techStacks.filter((stack) => !!stack.technologies.length).map((stack, index) => (
            <div key={index} className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-purple-400/30 transition-all duration-300">
              <div className="text-center mb-6">
                <div className="text-4xl mb-4">{stack.icon}</div>
                <h3 className="text-xl font-semibold text-white">{stack.category}</h3>
              </div>
              <div className="space-y-3">
                {stack.technologies.map((tech, techIndex) => (
                  <div key={techIndex} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <span className="text-gray-300">{tech.title}</span>
                    <div className="flex space-x-1">
                      {[...Array(5)].map((_, i) => (
                        <div key={i} className={`w-2 h-2 rounded-full ${tech.level < 6 ? 'bg-purple-300' : 'bg-gray-200'}`}></div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
