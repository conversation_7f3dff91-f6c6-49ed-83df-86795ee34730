import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "@/app/assets/styles/globals.scss";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Nam's Portfolio | Frontend Developer",
  description: "Personal portfolio showcasing my experience, hobbies, and technical skills as a frontend developer",
  keywords: ["frontend developer", "portfolio", "web development", "React", "Next.js"],
  authors: [{ name: "<PERSON>" }],
  icons: {
    icon: [
      { url: '/images/favicon.ico', sizes: '32x32', type: 'image/png' },
      { url: '/images/favicon.ico', sizes: '16x16', type: 'image/png' }
    ],
    shortcut: '/images/favicon.ico',
    apple: '/images/favicon.ico'
  }
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
